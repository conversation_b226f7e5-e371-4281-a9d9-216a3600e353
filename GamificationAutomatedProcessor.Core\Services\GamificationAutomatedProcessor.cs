﻿using GamificationAutomatedProcessor.Core.Helpers;
using GamificationAutomatedProcessor.Core.Interfaces;
using GamificationAutomatedProcessor.Core.Models;
using GamificationAutomatedProcessor.Core.Repositories;
using Library.Infrastructure.QueueService;
using Library.SlackService;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.Core.Services
{
    public interface IGamificationAutomatedProcessor
    {
        Task Process(DateTime today);
        Task ProcessKPIForUser(DateTime today, Game game, Team team, KPI kPI, TargetForTeams teamTarget, Coinsfor<PERSON><PERSON> kPICoins, Employee user);
    }
    public class GamificationAutomatedProcessor : IGamificationAutomatedProcessor
    {
        private readonly IGameRepository gameRepository;
        private readonly IQueueManager queueManager;
        private readonly ReportDataAPIsHelper reportDataAPIsHelper;
        private readonly ErrorMessenger errorMessenger;
        private readonly QueueHandlerService queueHandlerService;
        private const string KPI_PROCESSING_QUEUE = "gamification-automated-processor-queue";

        public GamificationAutomatedProcessor(IGameRepository gameRepository,
            IQueueManager queueManager,
            ReportDataAPIsHelper reportDataAPIsHelper,
            ErrorMessenger errorMessenger,
            QueueHandlerService queueHandlerService)
        {
            this.gameRepository = gameRepository;
            this.queueManager = queueManager;
            this.reportDataAPIsHelper = reportDataAPIsHelper;
            this.errorMessenger = errorMessenger;
            this.queueHandlerService = queueHandlerService;
        }
        public async Task Process(DateTime today)
        {
            var activeGames = await gameRepository.GetAllActiveGames(today);
            foreach (var game in activeGames)
            {
                var teams = await gameRepository.GetActiveTeamsForGame(game.Id);
                var monthlyKpis = await gameRepository.GetMonthlyKPIsForGame(game.Id);
                if (monthlyKpis.Count == 0)
                {
                    continue;
                }
                var teamCoins = await gameRepository.GetMonthlyKPIsCoinsForTeam(game.Id, monthlyKpis.Select(x => x.Id).ToList());
                foreach (var team in teams)
                {
                    var activeUsers = await gameRepository.GetActiveEmployeesInTeam(team.Id);
                    var teamTargets = await gameRepository.GetMonthlyKPIsTargetForTeam(team.Id, game.Id, monthlyKpis.Select(x => x.Id).ToList());
                    foreach (var kpi in monthlyKpis)
                    {
                        var teamTarget = teamTargets.ContainsKey(kpi.Id) ? teamTargets[kpi.Id] : null;
                        var kpiCoins = teamCoins.ContainsKey(kpi.Id) ? teamCoins[kpi.Id] : null;
                        if (teamTarget == null || kpiCoins == null)
                        {
                            continue;
                        }
                        teamTarget.KPISlabs = teamTarget.IsContinuous ? teamTarget.KPISlabs :
                                              teamTarget.KPISlabs?
                                              .OrderByDescending
                                              (x => AchievementCalculator.GetNumericValue(x.SlabTarget, kpi.Measure))
                                              .ToList();
                        foreach (var user in activeUsers)
                        {
                            // Add to queue instead of processing directly
                            var queueMessage = new GamificationKPIQueueMessage
                            {
                                Today = today,
                                Game = game,
                                Team = team,
                                KPI = kpi,
                                TeamTarget = teamTarget,
                                KPICoins = kpiCoins,
                                User = user
                            };
                            await queueHandlerService.AddToQueue(KPI_PROCESSING_QUEUE, queueMessage);
                            Console.WriteLine($"Added item to queue:\n{JsonConvert.SerializeObject(queueMessage)}");
                        }
                    }
                }
            }
        }

        public async Task ProcessKPIForUser(DateTime today, Game game, Team team, KPI kpi, TargetForTeams teamTarget, CoinsforKpi kpiCoins, Employee user)
        {
            var achievement = await GetAchievementForUser(user.CompanyId, user.Id, kpi.Id, game.StartDate, today);
            var gameKPIStat = new GameKPIStat();
            gameKPIStat.GameId = game.Id;
            gameKPIStat.IsQualifier = kpi.IsQualifier;
            gameKPIStat.KPIId = kpi.Id;
            gameKPIStat.KPITarget = teamTarget.Target;
            gameKPIStat.TeamId = team.Id;
            var target = AchievementCalculator.GetNumericValue(teamTarget.Target, kpi.Measure);
            var achievementval = AchievementCalculator.GetNumericValue(achievement.AchievedValue, kpi.Measure);
            gameKPIStat.IsKPIAchieved = achievement.IsAchieved || achievementval >= target;
            gameKPIStat.KPIAchievedValue = achievement.AchievedValue;
            if (teamTarget.KPISlabs != null && teamTarget.KPISlabs?.Count > 0)
            {
                if (teamTarget.IsContinuous)
                {
                    var slabs = teamTarget.KPISlabs;
                    // Case 1: Achievement is below the first slab
                    if (achievementval < AchievementCalculator.GetNumericValue(slabs[0].SlabTarget, kpi.Measure))
                    {
                        gameKPIStat.Payout = 0;
                        gameKPIStat.CoinsEarned = 0;
                        gameKPIStat.SlabId = slabs[0].Id;

                    }
                    // Case 3: Achievement is above the highest slab
                    else if (achievementval >= AchievementCalculator.GetNumericValue(slabs[slabs.Count - 1].SlabTarget, kpi.Measure))
                    {
                        gameKPIStat.Payout = slabs[slabs.Count - 1].SlabPayout;
                        gameKPIStat.CoinsEarned = slabs[slabs.Count - 1].SlabCoins;
                        gameKPIStat.SlabId = slabs[slabs.Count - 1].Id;
                    }
                    // Case 2: Achievement is within slabs
                    else
                    {
                        for (int i = 1; i < slabs.Count; i++)
                        {
                            if (achievementval >= AchievementCalculator.GetNumericValue(slabs[i - 1].SlabTarget, kpi.Measure))
                            {
                                var slabTargetDiff = AchievementCalculator.GetNumericValue(slabs[i].SlabTarget, kpi.Measure)
                                        - AchievementCalculator.GetNumericValue(slabs[i - 1].SlabTarget, kpi.Measure);

                                var slabTargetAchievedDiff = achievementval
                                    - AchievementCalculator.GetNumericValue(slabs[i - 1].SlabTarget, kpi.Measure);

                                var payout = slabs[i - 1].SlabPayout +
                                            (slabTargetAchievedDiff *
                                            (slabs[i].SlabPayout - slabs[i - 1].SlabPayout) / slabTargetDiff);

                                var coinsEarned = slabs[i - 1].SlabCoins +
                                                    (slabTargetAchievedDiff *
                                                    (slabs[i].SlabCoins - slabs[i - 1].SlabCoins) / slabTargetDiff);

                                gameKPIStat.Payout = (long?)payout;
                                gameKPIStat.CoinsEarned = (long)coinsEarned;
                                gameKPIStat.SlabId = slabs[i].Id;

                            }
                        }
                    }
                }
                else
                {
                    var slab = teamTarget.KPISlabs.FirstOrDefault(x => achievementval >= AchievementCalculator.GetNumericValue(x.SlabTarget, kpi.Measure));
                    gameKPIStat.CoinsEarned = slab != null ? achievement.IsAchieved && achievement.SlabId == slab.Id ? teamTarget.KPISlabs.Where(s => s.Id == achievement.SlabId).FirstOrDefault().SlabCoins : slab.SlabCoins : 0;
                    gameKPIStat.SlabId = slab != null ? slab.Id : (long?)null;
                    gameKPIStat.Payout = slab != null ? achievement.IsAchieved && achievement.SlabId == slab.Id ? teamTarget.KPISlabs.Where(s => s.Id == achievement.SlabId).FirstOrDefault().SlabPayout : slab.SlabPayout : 0;
                }
            }
            else
            {
                if ((kpi.Id == 89 || kpi.Id ==61 || kpi.Id == 150) && user.CompanyId == 172305)
                {
                    gameKPIStat.CoinsEarned = (long)(kpiCoins.Coins * achievementval);
                }
                else
                {
                    gameKPIStat.CoinsEarned = achievement.IsAchieved ? achievementval >= target ? kpiCoins.Coins : 0 : 0;
                }
            }
            ////gameKPIStat.SessionId = session;
            MinGameKPIStat stat = await gameRepository.SaveKPIUserStats(gameKPIStat,
            user.CompanyId, user.Id, today);
            await queueManager.AddToGamificationQueue(stat.Id.ToString(), new GamificationQueue()
            {
                CompanyId = user.CompanyId,
                EmployeeId = user.Id,
                QualifiedDateKey = stat.DateKey.ToString(),
            });
        }

        private async Task<Achievement> GetAchievementForUser(long companyId, long userId, long kpiId, DateTime startDate, DateTime endDate)
        {
            var api = $"api/Gamification/KpiAchievement?companyId={companyId}&esmId={userId}&kpiId={kpiId}&startDate={startDate:MM/dd/yyyy}&endDate={endDate:MM/dd/yyyy}";
            var data = await reportDataAPIsHelper.Get(api);

            return JsonConvert.DeserializeObject<Achievement>(data);
        }
    }
}
