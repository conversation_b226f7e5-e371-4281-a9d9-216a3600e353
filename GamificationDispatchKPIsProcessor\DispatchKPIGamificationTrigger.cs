﻿using Library.ResiliencyHelpers;
using Library.SlackService;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GamificationProcessor.Configuration;
using GamificationProcessor.Core.Models;
using GamificationProcessor.Core.Services;
using Library.AsyncLock;
using Microsoft.Azure.WebJobs;
using Newtonsoft.Json;


namespace DispatchKPIGamification
{
    public class DispatchKPIGamificationTrigger
    {
        private readonly IGamificationProcessor gamificationProcessor;

        public DispatchKPIGamificationTrigger(IGamificationProcessor gamificationProcessor)
        {
            this.gamificationProcessor = gamificationProcessor;
        }

        public async Task Process(DateTime forDate)
        {
            try
            {
                Console.WriteLine($"Running dispatch KPIs processor for Gamification for {forDate.ToShortDateString()}!");
                await gamificationProcessor.ProcessDispatchKPIsForGamification(forDate);
                Console.WriteLine($"Running dispatch KPIs processor for Gamification for {forDate.ToShortDateString()} Done!");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Some Error For Date {forDate.ToShortDateString()}!:\n{ex}");
                throw;
            }
        }


    }
}
