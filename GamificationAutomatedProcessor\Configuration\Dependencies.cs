﻿using GamificationAutomatedProcessor.Core.Helpers;
using GamificationAutomatedProcessor.Core.Interfaces;
using GamificationAutomatedProcessor.Core.Repositories;
using GamificationAutomatedProcessor.Core.Services;
using GamificationAutomatedProcessor.DbStorage.DbContexts;
using GamificationAutomatedProcessor.DbStorage.Repositories;
using Library.ConnectionStringParsor;
using Library.Infrastructure.QueueService;
using Library.ResiliencyHelpers;
using Library.SlackService;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;


namespace GamificationAutomatedProcessor.Configuration
{
    public static class Dependencies
    {
        public static string storageConnectionString = "";

        public static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.EnableRetryOnFailure();
        }

        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {

            storageConnectionString = configuration.GetConnectionString("StorageConnectionString");
            var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");
            serviceProvider.AddDbContext<MasterDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("MasterDbConnectionString"), SqlResiliencyBuilder), ServiceLifetime.Transient);


            serviceProvider.AddDbContext<WritableTransactionDbContext>(options =>
                    options.UseSqlServer(configuration.GetConnectionString("WritableTransactionDbConnectionString"), SqlResiliencyBuilder), ServiceLifetime.Transient);

            

            //helpers

            var reportApiConnection = ApiConnectionString.GetConnection(configuration.GetConnectionString("NSDataApiConnectionString"));

            var logReportProcessorEvent = configuration.GetValue<string>("AppSettings:logReportProcessorEvent");

            serviceProvider.AddSingleton(s => new AppConfigSettings
            {
                reportApiBaseUrl = reportApiConnection.BaseUrl,
                reportApiToken = reportApiConnection.AuthToken,
                logReportProcessorEvent = string.IsNullOrEmpty(logReportProcessorEvent) ? false : Convert.ToBoolean(logReportProcessorEvent),
            });
            serviceProvider.AddScoped<IGameRepository, GameRepository>();
            serviceProvider.AddScoped<IPositionCodeEntityMappingRepository, PositionCodeEntityMappingRepository>();
            serviceProvider.AddScoped<ReportDataAPIsHelper>(); ;
            serviceProvider.AddScoped<IGamificationAutomatedProcessor, Core.Services.GamificationAutomatedProcessor>();

            serviceProvider.AddSingleton<GamificationAutomatedProcessor>();

            //Infra
            serviceProvider.AddScoped((s) => new QueueHandlerService(storageConnectionString));
            serviceProvider.AddTransient<IQueueManager, QueueManager>();
            serviceProvider.AddTransient<IQueueHandler, CustomQueueHandler>();

            serviceProvider.AddScoped<ResilientAction>();


            serviceProvider.AddSingleton<IConfiguration>(c => configuration);

            serviceProvider.AddSingleton(s => new ErrorMessenger(masterStorageConnectionString, "GamificationAutomatedProcessor", "gamification"));

        }
    }
}
