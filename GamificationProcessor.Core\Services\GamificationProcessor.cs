using GamificationProcessor.Core.Helpers;
using GamificationProcessor.Core.Models;
using GamificationProcessor.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using System.Linq;

namespace GamificationProcessor.Core.Services
{
    public interface IGamificationProcessor
    {
        Task Process(GamificationQueueEvent queueEvent);
        Task ProcessDispatchKPIsForGamification(DateTime forDate);
        Task ProcessDispatchKPIDataForGame(long dispatchKpiId, string dispatchKpiTyped, long companyId, DateTime today, long gameId, int yearStartMonth);
    }
    //TODO: Create a New Processor base for all kinds of triggered processor that log the everything in a file and send it to slack
    public class GamificationProcessor : IGamificationProcessor
    {
        private readonly IGamificationRepository repo;
        private readonly MTD_LMTDDatesService mtdlmtdService;
        private readonly ICompanySettingsRepository companySettingRepository;

        public GamificationProcessor(IGamificationRepository repo, MTD_LMTDDatesService mtdlmtdService, ICompanySettingsRepository companySettingRepository)
        {
            this.repo = repo;
            this.mtdlmtdService = mtdlmtdService;
            this.companySettingRepository = companySettingRepository;

        }
        public async Task Process(GamificationQueueEvent queueEvent)
        {
            var companySettings = CompanySettings.Initialize(companySettingRepository.GetSettings(queueEvent.Data.CompanyId));
            var mtdLmtdDates = await mtdlmtdService.GetDates(queueEvent.Data.CompanyId, DateTime.ParseExact(queueEvent.Data.QualifiedDateKey,
                                        "yyyyMMdd",
                                        CultureInfo.InvariantCulture,
                                        DateTimeStyles.None), companySettings.YearStartMonth, true);
            await repo.SavePerspectiveData(Convert.ToInt64(queueEvent.Id), queueEvent.Data.CompanyId, queueEvent.Data.EmployeeId, mtdLmtdDates);


        }
        public async Task ProcessDispatchKPIsForGamification(DateTime today)
        {
            var KpisIdsForDispatch = repo.GetDispatchKPIIds();
            var activeCompanies = repo.GetAllActiveCompanies();
            foreach (var company in activeCompanies)
            {
                Console.WriteLine($"Started Running for {company.Id}-{company.Name}!");
                var settings = CompanySettings.Initialize(companySettingRepository.GetSettings(company.Id));
                var dayLimitforOrderDispatch = settings.DayLimitforOrderDispatch;

                var activeGames = await repo.GetDispatchGames(company.Id, dayLimitforOrderDispatch);
                foreach (var game in activeGames)
                {
                    foreach (var kpi in KpisIdsForDispatch)
                    {
                        await ProcessDispatchKPIDataForGame(kpi.Key, kpi.Value, company.Id, today, game, settings.YearStartMonth);
                    }
                }
                //TODO: Need to create a New Slack Logging Service
                Console.WriteLine($"Ended Running for{company.Id}-{company.Name}!");
            }
        }

        public async Task ProcessDispatchKPIDataForGame(long dispatchKpiId, string dispatchKpiType, long companyId, DateTime today, long gameId, int yearStartMonth)
        {
            var mtdLmtdDates = await mtdlmtdService.GetDates(companyId, today, yearStartMonth, true);
            var gameDetailed = await repo.GetGameDetailed(gameId);
            var teamIdsForGame = gameDetailed.TargetsforTeams.Select(t => t.TeamId).Distinct().ToList();
            var empList = await repo.GetGameUsers(companyId);
            var teamPlayers = empList.Where(a => teamIdsForGame.Contains(a.TeamId)).Select(b => b.UserId).ToList();
            
            if (gameDetailed.TargetsforTeams.Where(a => a.KpiId == dispatchKpiId).Count() > 0 && dispatchKpiType == "%DispatchAgainstOrder (Volume)")
            {
                var orderSummaryData = await repo.GetUsersDispatchAgainstOrderCalculation(companyId, teamPlayers, gameDetailed);
                if (orderSummaryData != null)
                {
                    foreach (var data in orderSummaryData)
                    {
                        var teamId = empList.Where(a => a.UserId == data.UserId).Select(b => b.TeamId).FirstOrDefault();
                        await repo.SaveDispatchKPIData(companyId, gameDetailed, data, teamId, dispatchKpiId, mtdLmtdDates);
                    }
                }
            }
            else if (gameDetailed.TargetsforTeams.Where(a => a.KpiId == dispatchKpiId).Count() > 0 && dispatchKpiType == "% Order Validation")
            {
                var orderSummaryData = await repo.GetUsersOrderValidationCalculation(companyId, teamPlayers, gameDetailed);
                if (orderSummaryData != null)
                {
                    foreach (var data in orderSummaryData)
                    {
                        var teamId = empList.Where(a => a.UserId == data.UserId).Select(b => b.TeamId).FirstOrDefault();
                        await repo.SaveDispatchKPIData(companyId, gameDetailed, data, teamId, dispatchKpiId, mtdLmtdDates);
                    }
                }
            }
        }
    }
}
