using GamificationAutomatedProcessor.Core.Models;
using GamificationAutomatedProcessor.Core.Services;
using GamificationKPIProcessor.Configuration;
using Microsoft.Azure.WebJobs;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace GamificationKPIProcessor
{
    public class GamificationKPIProcessor
    {
        private readonly IGamificationAutomatedProcessor gamificationAutomatedProcessor;

        public GamificationKPIProcessor(IGamificationAutomatedProcessor gamificationAutomatedProcessor)
        {
            this.gamificationAutomatedProcessor = gamificationAutomatedProcessor;
        }

        [FunctionName("ProcessGamificationKPIQueue")]
        public async Task ProcessGamificationKPIQueue([QueueTrigger(Dependencies.GamificationKPIQueue)] string queueMessage)
        {
            try
            {
                var queueItem = JsonConvert.DeserializeObject<GamificationKPIQueueMessage>(queueMessage);
                Console.WriteLine($"Processing queue item: {queueItem}");
                
                await Process(queueItem);
                
                Console.WriteLine($"Successfully processed KPI for User: {queueItem.User.Id}, Game: {queueItem.Game.Id}, KPI: {queueItem.KPI.Id}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GamificationKPIProcessor - Queue Item:\n{queueMessage}\n{ex}");
                throw;
            }
        }

        private async Task Process(GamificationKPIQueueMessage queueMessage)
        {
            await gamificationAutomatedProcessor.ProcessKPIForUser(
                queueMessage.Today,
                queueMessage.Game,
                queueMessage.Team,
                queueMessage.KPI,
                queueMessage.TeamTarget,
                queueMessage.KPICoins,
                queueMessage.User
            );
        }
    }
}
