﻿using GamificationProcessor.Core.Services.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Libraries.CommonModels;
using GamificationProcessor.Core.Models;

namespace GamificationProcessor.Core.Repositories
{
    public interface IGamificationRepository
    {
        Task SavePerspectiveData(long id, long companyId, long esmId, FA_MTD_LMTD fA_MTD_LMTD_Dates);
        List<EntityMin> GetAllActiveCompanies();
        Task<Game> GetGameDetailed(long gameId);
        Task<List<long>> GetDispatchGames(long companyId, int DayLimitforOrderDispatch);
        Task<List<GameUser>> GetGameUsers(long companyId);
        Task<List<GameUser>> GetUsersDispatchAgainstOrderCalculation(long companyId, List<long> gameUserIds, Game game);
        Task<List<GameUser>> GetUsersOrderValidationCalculation(long companyId, List<long> gameUserIds, Game game);
        Task SaveDispatchKPIData(long companyId, Game game, GameUser data, long teamId, long kpiId, FA_MTD_LMTD fA_MTD_LMTD_Dates);
        Dictionary<long,string> GetDispatchKPIIds();
    }
}
