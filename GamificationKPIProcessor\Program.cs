using GamificationKPIProcessor.Configuration;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace GamificationKPIProcessor
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((config) =>
                {
                    var env = Environment.GetEnvironmentVariable("BuildEnvironment");
                    config.AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
                        .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
                        .AddEnvironmentVariables();
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddAzureStorage(c => {

                        c.BatchSize =
#if DEBUG
                         1;
#else
                        5;
#endif
                        c.MaxPollingInterval = TimeSpan.FromSeconds(5);
                    });
                })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .ConfigureLogging((context, b) =>
                {
                    b.AddConsole();
                });

            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }
    }
}
